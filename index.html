<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8" />
  <meta name="viewport" content="width=device-width,initial-scale=1" />
  <meta name="description" content="<PERSON><PERSON><PERSON><PERSON> - Software Engineer, ML Data Analyst, Technical Manager with 7+ years experience. AWS Certified Solutions Architect." />
  <meta name="keywords" content="software engineer, machine learning, data analyst, technical manager, full-stack developer, AWS certified" />
  <title><PERSON><PERSON><PERSON><PERSON> — Resume</title>
  <style>
    :root{
      --bg:#fcfcfc; --card:#ffffff; --muted:#64748b; --accent:#2563eb; --text:#1e293b; --maxw:920px;
      --sans: Georgia, "Times New Roman", serif;
      --mono: "SFMono-Regular", Consolas, "Liberation Mono", Menlo, monospace;
      --radius: 8px; --shadow: 0 1px 3px rgb(0 0 0 / 0.1);
      --border: #e2e8f0;
    }
    html,body{height:100%; font-size: 16px; line-height: 1.5;}
    body{
      margin:0;
      font-family:var(--sans);
      background:var(--bg);
      color:var(--text);
      -webkit-font-smoothing:antialiased;
      -moz-osx-font-smoothing:grayscale;
    }
    .container{
      max-width:var(--maxw);
      margin:32px auto;
      padding:24px;
    }
    .card{
      background:var(--card);
      border-radius:var(--radius);
      box-shadow:var(--shadow);
      border: 1px solid var(--border);
      padding:28px;
      margin-bottom:24px;
    }
    header{display:flex;align-items:center;justify-content:space-between;gap:20px;flex-wrap:wrap}
    .title{
      line-height:1.2;
    }
    h1{font-size:28px;margin:0;font-weight:400;color:#1e293b;}
    .subtitle{color:var(--muted); margin-top:8px;font-size:16px;font-weight:400;line-height:1.4;font-style:italic;}
    .actions{display:flex;gap:12px;align-items:center}
    .btn{
      background:var(--accent); color:white; border:none;padding:10px 16px;border-radius:var(--radius);cursor:pointer;font-weight:500;
      font-size:14px; transition: background-color 0.2s ease; font-family: var(--sans);
    }
    .btn:hover{background:#1d4ed8;}
    .btn:focus{outline:2px solid var(--accent);outline-offset:2px;}
    .btn.secondary{background:white;color:var(--accent);border:1px solid var(--accent);}
    .btn.secondary:hover{background:#f8fafc;}
    .btn.secondary:focus{outline:2px solid var(--accent);outline-offset:2px;}
    a{color:var(--accent);text-decoration:none;}
    a:hover{text-decoration:underline;}
    a:focus{outline:2px solid var(--accent);outline-offset:2px;border-radius:4px;}
    .grid{display:grid;grid-template-columns:1fr 340px;gap:24px}
    @media (max-width:920px){ 
      .grid{grid-template-columns:1fr} 
      .container{margin:16px auto;padding:16px;}
      .card{padding:20px;margin-bottom:16px;}
      h1{font-size:28px;}
      .subtitle{font-size:16px;}
      header{flex-direction:column;align-items:flex-start;gap:16px;}
      .actions{width:100%;justify-content:center;}
    }
    @media (max-width:480px){
      h1{font-size:24px;}
      .subtitle{font-size:15px;}
      .card{padding:16px;}
      .btn{padding:10px 14px;font-size:13px;}
    }
    section h2{margin:0 0 16px 0;font-size:18px;color:#1e293b;font-weight:500;border-bottom:1px solid var(--border);padding-bottom:8px;}
    p.lead{color:var(--text);margin:0 0 16px 0;font-size:16px;line-height:1.6;font-weight:400;}
    ul{margin:12px 0 16px 20px;line-height:1.5;}
    li{margin-bottom:6px;}
    .skills p{margin:10px 0;line-height:1.6;}
    .meta{color:var(--muted);font-size:14px;line-height:1.5;}
    .project{margin-bottom:20px;padding-bottom:16px;border-bottom:1px solid #f1f5f9;}
    .project:last-child{border-bottom:none;margin-bottom:0;padding-bottom:0;}
    footer{text-align:center;color:var(--muted);font-size:14px;margin-top:16px}
    code.kv{background:#f8fafc;padding:3px 8px;border-radius:4px;color:#1e293b;font-weight:500;font-family:var(--mono);font-size:13px;border:1px solid var(--border);}
    .print-hide{display:inline-block}
    .role{margin-bottom:24px;padding-bottom:16px;border-bottom:1px solid #f1f5f9;}
    .role:last-child{border-bottom:none;margin-bottom:0;padding-bottom:0;}
    .role strong{color:var(--text);font-size:16px;font-weight:600;}
    @media print{ .print-hide{display:none} .container{margin:0;padding:0} body{background:white} .card{box-shadow:none;border-radius:0;border:none;} }
  </style>
</head>
<body>
  <div class="container">

    <div class="card">
      <header>
        <div class="title">
          <h1>Zhifei Mi</h1>
          <div class="subtitle">Target Role: Software Engineer / ML Data Analyst / Technical Manager</div>
          <div style="margin-top:12px;">
            <span style="background:#f1f5f9;color:#475569;padding:4px 12px;border-radius:4px;font-size:13px;font-weight:500;">Available Immediately</span>
            <span style="background:#f1f5f9;color:#475569;padding:4px 12px;border-radius:4px;font-size:13px;font-weight:500;margin-left:8px;">7+ Years Experience</span>
          </div>
        </div>

        <div class="actions print-hide">
          <button class="btn" onclick="window.print()">Download PDF</button>
          <button class="btn secondary" onclick="downloadVCard()">Contact Card</button>
        </div>
      </header>
    </div>

    <div class="grid">

      <!-- main column -->
      <div>
        <div class="card">
          <section>
            <h2>Professional Profile</h2>
            <p class="lead">
              <strong>Versatile software professional with 7+ years of experience</strong> driving innovation across software engineering,
              machine learning, data analysis, and team leadership. <strong>Proven track record</strong> delivering enterprise solutions in supply chain, IoT, construction, and hybrid cloud environments.
              Advanced from Software Engineer to Software Group Manager. <strong>AWS Certified Solutions Architect – Professional.</strong>
            </p>

            <div class="achievements">
              <h3 style="font-size:16px;color:var(--accent);margin:16px 0 12px 0;font-weight:600;">Key Achievements & Capabilities</h3>
              <ul>
                <li><strong>Team Leadership:</strong> Built and led engineering teams delivering large-scale enterprise platforms with measurable business impact.</li>
                <li><strong>Data Science & ML:</strong> Developed data-driven features using Python (pandas, scikit-learn, TensorFlow, PyTorch) for actionable insights.</li>
                <li><strong>Full-Stack Development:</strong> Designed Java Spring microservices and Vue/React front ends with modern architecture patterns.</li>
                <li><strong>Strategic Analysis:</strong> Applied statistical analysis and visualization tools to inform critical business decisions.</li>
                <li><strong>DevOps Excellence:</strong> Experienced with CI/CD pipelines, Docker, Linux, and DB optimization (SQL & NoSQL).</li>
              </ul>
            </div>
          </section>
        </div>

        <div class="card">
          <section>
            <h2>Professional Experience</h2>

            <div class="role">
              <strong>China DataCom Corporation Limited</strong><br>
              <span class="meta" style="font-size:15px;color:var(--accent);font-weight:600;">Software Group Leader / Software Engineer</span><br>
              <span class="meta">Aug 2021 – Sep 2022 (Leader) | Jul 2017 – Jul 2021 (Engineer)</span>

              <div style="margin-top:12px;">
                <h4 style="margin:12px 0 8px 0;color:var(--text);font-size:14px;font-weight:600;text-transform:uppercase;letter-spacing:0.5px;">Leadership & Management</h4>
                <ul>
                  <li><strong>Team Leadership:</strong> Led a cross-functional team on enterprise web platforms, owning end-to-end delivery and achieving 98% on-time project completion.</li>
                  <li><strong>Process Optimization:</strong> Defined and optimized development processes including code reviews, branching strategy, CI/CD, and defect triage, reducing bugs by 40%.</li>
                  <li><strong>Strategic Planning:</strong> Guided requirements analysis, architecture documentation, and performance reporting for C-level stakeholders.</li>
                </ul>
                
                <h4 style="margin:16px 0 8px 0;color:var(--text);font-size:14px;font-weight:600;text-transform:uppercase;letter-spacing:0.5px;">Technical Contributions</h4>
                <ul>
                  <li><strong>Full-Stack Development:</strong> Implemented scalable features in Java/Spring, maintained Vue.js and jQuery front ends, ensuring 99.9% production stability.</li>
                  <li><strong>System Reliability:</strong> Improved monitoring, error handling, and incident recovery workflows, reducing downtime by 60%.</li>
                </ul>
              </div>
            </div>

            <p class="meta"><em>Additional experience:</em> Data analysis and visualization during postgraduate study (Jul 2023 – Jul 2025).</p>
          </section>
        </div>

        <div class="card">
          <section>
            <h2>Featured Projects</h2>

            <div class="project">
              <div style="margin-bottom:8px;">
                <strong style="color:var(--text);font-size:16px;">Full-Process Visualization Management System</strong>
                <span class="meta" style="margin-left:12px;font-style:italic;">(Supply Chain)</span>
              </div>
              <p class="meta" style="margin-bottom:12px;">Spring Boot, MyBatis, MySQL/Postgres, Redis</p>
              <ul>
                <li><strong>Cross-subsidiary integration:</strong> Designed modules for supply-chain visibility and workflows across multiple business units.</li>
                <li><strong>Operational efficiency:</strong> Improved inter-branch coordination and reduced issue resolution times by 45% via dashboards and automated alerts.</li>
              </ul>
            </div>

            <div class="project">
              <div style="margin-bottom:8px;">
                <strong style="color:var(--text);font-size:16px;">Construction Committee Integrated Management Platform</strong>
                <span class="meta" style="margin-left:12px;font-style:italic;">(Enterprise)</span>
              </div>
              <p class="meta" style="margin-bottom:12px;">Vue.js, ElementUI, OAuth2/JWT, Swagger</p>
              <ul>
                <li><strong>Comprehensive modules:</strong> Developed project, personnel and payroll management modules with standardized UI patterns.</li>
                <li><strong>User experience:</strong> Created intuitive interfaces reducing training time by 30% and increasing user adoption.</li>
              </ul>
            </div>

            <div class="project">
              <div style="margin-bottom:8px;">
                <strong style="color:var(--text);font-size:16px;">Wisely-Cloud Multi-Cloud Hybrid Management Platform</strong>
                <span class="meta" style="margin-left:12px;font-style:italic;">(Cloud Infrastructure)</span>
              </div>
              <p class="meta" style="margin-bottom:12px;">Docker, Jenkins, Nginx/Tomcat, multi-cloud APIs</p>
              <ul>
                <li><strong>Unified infrastructure:</strong> Integrated inventory, access control and on-demand resource allocation across clouds and on-premises servers.</li>
                <li><strong>Cost optimization:</strong> Enabled dynamic resource allocation reducing infrastructure costs by 25%.</li>
              </ul>
            </div>
          </section>
        </div>

        <div class="card">
          <section>
            <h2>Education</h2>
            <ul>
              <li><strong>Master of Computer Science (Machine Learning & Big Data; Software Engineering, with Distinction)</strong>, University of Wollongong — 2023–2025</li>
              <li><strong>Master of Engineering</strong>, Arizona State University — 2021–2023 (GPA 3.93/4)</li>
              <li><strong>Bachelor of Engineering (Electronic Information Engineering)</strong>, Inner Mongolia University of Technology — 2013–2017</li>
            </ul>
          </section>
        </div>
      </div>

      <!-- sidebar -->
      <aside>
        <div class="card">
          <section>
            <h2>Core Skills</h2>
            <div class="skill-category">
              <p><strong>Backend:</strong> Java (Spring Boot/Cloud, Hibernate, MyBatis), Python (Flask), REST APIs</p>
              <p><strong>Frontend:</strong> React, Vue, TypeScript, HTML5, CSS3/SCSS, jQuery</p>
              <p><strong>Databases:</strong> MySQL, PostgreSQL, Oracle, MongoDB, Redis</p>
              <p><strong>Data & ML:</strong> PyTorch, TensorFlow, Spark, Hadoop, pandas, scikit-learn</p>
              <p><strong>DevOps:</strong> Linux, Nginx, Jenkins, Docker, CI/CD, Git/SVN</p>
              <p><strong>Leadership:</strong> Agile/Scrum, mentoring, architecture reviews</p>
            </div>
          </section>
        </div>

        <div class="card">
          <section>
            <h2>Certifications</h2>
            <ul>
              <li>ACS Certified Software Engineer</li>
              <li>AWS Certified Solutions Architect – Professional</li>
            </ul>
          </section>
        </div>

        <div class="card">
          <section>
            <h2>Leadership Experience</h2>
            <ul>
              <li>Mentored junior engineers in design, testing and code reviews</li>
              <li>Defined CI/CD and PR workflows and quality gates</li>
              <li>Coordinated product and ops to align delivery priorities</li>
              <li>Led root-cause analysis and post-incident reviews to prevent regressions</li>
            </ul>
          </section>
        </div>

        <div class="card">
          <section>
            <h2>Contact Information</h2>
            <div style="margin-bottom:16px;">
              <p style="color:var(--text);font-weight:500;margin-bottom:12px;">Available for new opportunities</p>
              <p class="meta" style="margin-bottom:16px;">References available upon request.</p>
            </div>
            
            <div style="margin:16px 0;">
              <div style="display:flex;align-items:center;">
                 <strong style="margin-right:12px;color:var(--text);font-size:14px;">X:</strong>
                 <span class="kv"><a href="https://x.com/zhifeimi" style="text-decoration:none;">@zhifeimi</a></span>
              </div>
              <div style="display:flex;align-items:center;">
                <strong style="margin-right:12px;color:var(--text);font-size:14px;">LinkedIn:</strong>
                <span class="kv"><a href="https://www.linkedin.com/in/zhifeimi/" style="text-decoration:none;">Zhifei Mi</a></span>
              </div>
              <div style="display:flex;align-items:center;">
                <strong style="margin-right:12px;color:var(--text);font-size:14px;">Facebook:</strong>
                <span class="kv"><a href="https://www.facebook.com/mizhifei" style="text-decoration:none;">@mizhifei</a></span>
              </div>
              <div style="display:flex;align-items:center;margin-bottom:12px;">
                <strong style="margin-right:12px;color:var(--text);font-size:14px;">Email:</strong>
                <span class="kv"><a href="mailto:<EMAIL>" style="text-decoration:none;"><EMAIL></a></span>
              </div>
            </div>
          </section>
        </div>

      </aside>
    </div>

    <footer class="card">
      <div class="meta" style="text-align:center">© <span id="year"></span> Zhifei Mi — Resume</div>
    </footer>
  </div>

  <script>
    document.getElementById('year').textContent = new Date().getFullYear();

    function downloadVCard(){
      const name = 'Zhifei Mi';
      const email = '<EMAIL>';
      const phone = '+610419175679';
      const title = 'Software Engineer / ML Data Analyst / Technical Manager';
      const vcard =
`BEGIN:VCARD
VERSION:3.0
FN:${name}
TITLE:${title}
TEL;TYPE=WORK,VOICE:${phone}
EMAIL;TYPE=INTERNET:${email}
END:VCARD`;
      const blob = new Blob([vcard], {type:'text/vcard'});
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url; a.download = 'Zhifei_Mi.vcf';
      document.body.appendChild(a);
      a.click();
      a.remove();
      URL.revokeObjectURL(url);
    }
  </script>
</body>
</html>
